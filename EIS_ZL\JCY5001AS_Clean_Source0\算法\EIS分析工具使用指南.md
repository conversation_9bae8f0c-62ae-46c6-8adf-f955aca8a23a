# 增强版EIS分析工具使用指南

## 概述

增强版EIS（电化学阻抗谱）分析工具是一个功能强大的Python库，专门用于分析电化学阻抗谱数据。该工具提供了完整的EIS数据分析流程，包括数据加载、质量验证、奈奎斯特图绘制、等效电路拟合、参数提取和结果可视化。

## 主要功能

### 1. 数据处理
- 支持多种数据格式（CSV、Excel、字典、DataFrame）
- 自动列名标准化和数据验证
- 数据质量评估和异常检测

### 2. 可视化功能
- **奈奎斯特图**：支持交互式点击查看数据点信息
- **Bode图**：阻抗幅值和相位随频率变化
- **残差分析图**：评估拟合质量
- 支持频率标注和高亮显示

### 3. 等效电路模型
- **Randles电路**：Rs + (Rct || CPE)
- **双RC电路**：Rs + (R1||C1) + (R2||C2)  
- **Warburg电路**：Rs + (Rct || CPE) + W
- 自动参数初值估计和边界设置

### 4. 分析功能
- 智能参数提取算法
- 多模型拟合效果比较
- 拟合质量评估（RMSE、R²、χ²）
- 综合分析报告生成

## 快速开始

### 安装依赖

```bash
pip install numpy pandas matplotlib scipy
```

### 基本使用示例

```python
from enhanced_eis_analyzer import EnhancedEISAnalyzer, CircuitModel

# 1. 创建分析器
analyzer = EnhancedEISAnalyzer()

# 2. 加载数据
# 方式1：从CSV文件
analyzer.load_data('your_eis_data.csv')

# 方式2：从字典
data = {
    'freq': [0.01, 0.1, 1, 10, 100, 1000],  # Hz
    'z_real': [20.5, 18.2, 15.8, 12.1, 8.5, 5.2],  # Ω
    'z_imag': [0.1, -2.3, -5.8, -8.2, -6.1, -1.2]  # Ω
}
analyzer.load_data(data)

# 3. 绘制奈奎斯特图
fig = analyzer.plot_nyquist(interactive=True)
plt.show()

# 4. 拟合等效电路
result = analyzer.fit_circuit_model(CircuitModel.RANDLES)
print(f"Rs = {result.parameters['Rs']:.3f} Ω")
print(f"Rct = {result.parameters['Rct']:.3f} Ω")

# 5. 生成分析报告
report = analyzer.generate_analysis_report()
```

## 数据格式要求

### CSV文件格式
```csv
freq,z_real,z_imag
0.01,20.5,0.1
0.1,18.2,-2.3
1.0,15.8,-5.8
10.0,12.1,-8.2
100.0,8.5,-6.1
1000.0,5.2,-1.2
```

### 支持的列名
- 频率：`freq`, `frequency`, `f`, `freq_hz`
- 实部：`z_real`, `real`, `impedance_real`, `z'`
- 虚部：`z_imag`, `imag`, `impedance_imag`, `z''`

## 详细功能说明

### 奈奎斯特图绘制

```python
# 基本奈奎斯特图
fig = analyzer.plot_nyquist()

# 交互式奈奎斯特图（点击查看数据点信息）
fig = analyzer.plot_nyquist(interactive=True)

# 高亮特定频率点
highlight_freqs = [0.1, 1.0, 10.0, 100.0]
fig = analyzer.plot_nyquist(highlight_frequencies=highlight_freqs)

# 显示拟合曲线
fig = analyzer.plot_nyquist(show_fit=True)
```

### 等效电路拟合

```python
# Randles电路拟合
randles_result = analyzer.fit_circuit_model(CircuitModel.RANDLES)

# 双RC电路拟合
double_rc_result = analyzer.fit_circuit_model(CircuitModel.DOUBLE_RC)

# Warburg电路拟合
warburg_result = analyzer.fit_circuit_model(CircuitModel.WARBURG)

# 自定义初始参数
initial_guess = {'Rs': 5.0, 'Rct': 20.0, 'CPE_T': 1e-5, 'CPE_n': 0.8}
result = analyzer.fit_circuit_model(CircuitModel.RANDLES, initial_guess=initial_guess)
```

### Bode图绘制

```python
# 绘制阻抗幅值和相位图
fig = analyzer.plot_bode(show_fit=True)
plt.show()
```

### 残差分析

```python
# 分析拟合残差
fig = analyzer.plot_residuals(CircuitModel.RANDLES)
plt.show()
```

### 模型比较

```python
# 拟合多个模型
analyzer.fit_circuit_model(CircuitModel.RANDLES)
analyzer.fit_circuit_model(CircuitModel.DOUBLE_RC)

# 比较拟合效果
comparison = analyzer.compare_models()
print(comparison)
```

## 参数说明

### Randles电路参数
- **Rs**：溶液电阻（Ω），表示电解质的欧姆阻抗
- **Rct**：电荷转移电阻（Ω），表示电极反应的动力学阻抗
- **CPE_T**：常相位元件参数（F·s^(n-1)），与双电层电容相关
- **CPE_n**：常相位元件指数（0-1），理想电容为1，纯电阻为0

### 拟合质量指标
- **RMSE**：均方根误差，越小越好
- **R²**：决定系数，越接近1越好
- **χ²**：卡方值，用于评估拟合优度

## 实际应用示例

### 电池阻抗分析
```python
# 加载电池EIS数据
analyzer.load_data('battery_eis.csv')

# 拟合Randles电路
result = analyzer.fit_circuit_model(CircuitModel.RANDLES)

# 提取关键参数
Rs = result.parameters['Rs']  # 内阻
Rct = result.parameters['Rct']  # 电荷转移阻抗

print(f"电池内阻: {Rs:.2f} mΩ")
print(f"电荷转移阻抗: {Rct:.2f} mΩ")

# 绘制分析图
analyzer.plot_nyquist(show_fit=True)
analyzer.plot_bode(show_fit=True)
```

### 腐蚀研究
```python
# 分析腐蚀过程的阻抗变化
analyzer.load_data('corrosion_eis.csv')

# 使用双RC模型（可能有多个时间常数）
result = analyzer.fit_circuit_model(CircuitModel.DOUBLE_RC)

# 分析腐蚀参数
if result.success:
    print("腐蚀体系分析结果：")
    for param, value in result.parameters.items():
        print(f"{param}: {value:.3e}")
```

## 注意事项

1. **数据质量**：确保数据点足够多（建议>20个）且频率范围足够宽
2. **单位一致性**：确保阻抗数据单位一致（Ω或mΩ）
3. **物理合理性**：检查拟合参数是否在物理合理范围内
4. **模型选择**：根据体系特点选择合适的等效电路模型

## 故障排除

### 常见问题
1. **拟合失败**：检查数据质量，尝试调整初始参数
2. **参数不合理**：检查数据单位，考虑使用其他模型
3. **图形显示问题**：确保安装了matplotlib并正确配置中文字体

### 调试技巧
```python
# 检查数据质量
quality_report = analyzer._validate_data_quality()
print(quality_report)

# 查看拟合详情
if result.success:
    print(f"拟合消息: {result.message}")
    print(f"参数误差: {result.parameter_errors}")
else:
    print(f"拟合失败原因: {result.message}")
```

## 扩展功能

该工具设计为可扩展的，您可以：
1. 添加新的等效电路模型
2. 自定义可视化样式
3. 集成到现有的数据分析流程中
4. 批量处理多个EIS数据文件

## 技术支持

如有问题或建议，请联系开发团队或查看项目文档。
