#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析您提供的奈奎斯特图数据
根据您图片中的数据特征创建相似的EIS数据并进行分析

作者：Jack
日期：2025-01-27
"""

import numpy as np
import matplotlib.pyplot as plt
from enhanced_eis_analyzer import EnhancedEISAnalyzer, CircuitModel
import pandas as pd

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False


def create_data_like_your_image():
    """
    根据您提供的奈奎斯特图创建相似的数据
    从图片可以看出：
    - 实部范围：约13000-19000 μΩ
    - 虚部范围：约-1500到1000 μΩ
    - 典型的半圆形状，表明是电池或电化学系统
    """
    print("正在创建类似您图片的EIS数据...")
    
    # 频率范围：从低频到高频
    freq = np.logspace(-1, 5, 80)  # 0.1 Hz to 100 kHz
    omega = 2 * np.pi * freq
    
    # 根据图片估计的参数（单位：μΩ）
    Rs = 13500    # 溶液电阻，对应高频截距
    Rct = 5500    # 电荷转移电阻，对应半圆直径
    CPE_T = 8e-4  # CPE参数，调整以匹配半圆形状
    CPE_n = 0.88  # CPE指数，接近1表示接近理想电容
    
    # 计算Randles电路阻抗
    Z_CPE = 1 / (CPE_T * (1j * omega)**CPE_n)
    Z_parallel = (Rct * Z_CPE) / (Rct + Z_CPE)
    Z_total = Rs + Z_parallel
    
    # 添加真实的测量噪声（约±50 μΩ）
    noise_level = 50
    noise_real = np.random.normal(0, noise_level, len(freq))
    noise_imag = np.random.normal(0, noise_level, len(freq))
    
    Z_total += noise_real + 1j * noise_imag
    
    # 确保数据在合理范围内
    Z_total.real = np.clip(Z_total.real, 13000, 19500)
    Z_total.imag = np.clip(Z_total.imag, -1500, 1200)
    
    return {
        'freq': freq,
        'z_real': Z_total.real,
        'z_imag': Z_total.imag
    }


def analyze_your_data():
    """分析类似您图片的数据"""
    print("=== 分析您的奈奎斯特图数据 ===")
    
    # 创建分析器
    analyzer = EnhancedEISAnalyzer()
    
    # 加载数据
    data = create_data_like_your_image()
    analyzer.load_data(data)
    
    print(f"数据加载完成：")
    print(f"  数据点数: {len(data['freq'])}")
    print(f"  频率范围: {data['freq'].min():.1f} - {data['freq'].max():.0f} Hz")
    print(f"  实部范围: {data['z_real'].min():.0f} - {data['z_real'].max():.0f} μΩ")
    print(f"  虚部范围: {data['z_imag'].min():.0f} - {data['z_imag'].max():.0f} μΩ")
    
    return analyzer, data


def plot_comparison_with_your_image(analyzer):
    """绘制与您图片相似的奈奎斯特图"""
    print("\n=== 绘制奈奎斯特图（类似您的图片）===")
    
    # 创建图形，使用与您图片相似的样式
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 绘制数据点，使用多种颜色表示不同频率
    scatter = ax.scatter(analyzer.data.z_real, -analyzer.data.z_imag, 
                        c=np.log10(analyzer.data.frequency), 
                        cmap='tab10',  # 使用多彩色调
                        s=60, 
                        alpha=0.8, 
                        edgecolors='black', 
                        linewidth=0.5)
    
    # 连接数据点形成曲线
    ax.plot(analyzer.data.z_real, -analyzer.data.z_imag, 
           'k-', alpha=0.3, linewidth=1)
    
    # 设置坐标轴
    ax.set_xlabel('实部 Z\' (μΩ)', fontsize=14, fontweight='bold')
    ax.set_ylabel('虚部 Z\'\' (μΩ)', fontsize=14, fontweight='bold')
    ax.set_title('奈奎斯特图', fontsize=16, fontweight='bold')
    
    # 设置坐标轴范围（与您的图片相似）
    ax.set_xlim(13000, 19500)
    ax.set_ylim(-1500, 1200)
    
    # 添加网格
    ax.grid(True, alpha=0.3)
    
    # 添加颜色条
    cbar = plt.colorbar(scatter, ax=ax)
    cbar.set_label('log₁₀(频率/Hz)', fontsize=12)
    
    # 设置等比例坐标轴
    ax.set_aspect('equal', adjustable='box')
    
    plt.tight_layout()
    plt.show()
    
    return fig


def fit_and_analyze(analyzer):
    """拟合等效电路并分析参数"""
    print("\n=== 等效电路拟合分析 ===")
    
    # 拟合Randles电路
    print("正在拟合Randles电路...")
    randles_result = analyzer.fit_circuit_model(CircuitModel.RANDLES)
    
    if randles_result.success:
        print("✓ Randles电路拟合成功！")
        params = randles_result.parameters
        
        print(f"\n提取的电化学参数：")
        print(f"  Rs (溶液电阻) = {params['Rs']:.0f} μΩ")
        print(f"  Rct (电荷转移电阻) = {params['Rct']:.0f} μΩ")
        print(f"  CPE_T (双电层电容参数) = {params['CPE_T']:.2e} F·s^(n-1)")
        print(f"  CPE_n (CPE指数) = {params['CPE_n']:.3f}")
        
        print(f"\n拟合质量评估：")
        print(f"  RMSE (均方根误差) = {randles_result.rmse:.1f} μΩ")
        print(f"  R² (决定系数) = {randles_result.r_squared:.4f}")
        
        # 物理意义解释
        print(f"\n物理意义解释：")
        total_resistance = params['Rs'] + params['Rct']
        print(f"  总内阻 = Rs + Rct = {total_resistance:.0f} μΩ")
        
        if params['CPE_n'] > 0.9:
            print(f"  CPE接近理想电容行为 (n={params['CPE_n']:.3f})")
        elif params['CPE_n'] < 0.7:
            print(f"  CPE显示明显的非理想行为 (n={params['CPE_n']:.3f})")
        else:
            print(f"  CPE显示中等程度的非理想行为 (n={params['CPE_n']:.3f})")
            
        # 估算特征频率
        if params['Rct'] > 0 and params['CPE_T'] > 0:
            # 简化估算：f_char ≈ 1/(2π * Rct * CPE_T)
            f_char = 1 / (2 * np.pi * params['Rct'] * params['CPE_T'])
            print(f"  估算特征频率 ≈ {f_char:.1f} Hz")
    
    else:
        print("✗ Randles电路拟合失败")
        print(f"失败原因: {randles_result.message}")
    
    return randles_result


def plot_fitted_nyquist(analyzer):
    """绘制带拟合曲线的奈奎斯特图"""
    print("\n=== 绘制拟合结果对比图 ===")
    
    fig = analyzer.plot_nyquist(show_fit=True)
    plt.suptitle('奈奎斯特图 - 实验数据与Randles电路拟合对比', 
                fontsize=16, fontweight='bold')
    plt.show()
    
    return fig


def generate_analysis_report(analyzer):
    """生成详细分析报告"""
    print("\n=== 生成分析报告 ===")
    
    report = analyzer.generate_analysis_report()
    
    print("数据质量评估：")
    quality = report['data_quality']
    if quality['is_valid']:
        print("  ✓ 数据质量良好")
    else:
        print("  ⚠ 数据质量问题：")
        for issue in quality['issues']:
            print(f"    - {issue}")
    
    print(f"\n数据统计信息：")
    data_info = report['data_info']
    print(f"  数据点数: {data_info['data_points']}")
    print(f"  频率范围: {data_info['frequency_range']['min']:.1f} - {data_info['frequency_range']['max']:.0f} Hz")
    print(f"  频率跨度: {data_info['frequency_range']['ratio']:.0f} 倍")
    
    if report['recommendations']:
        print(f"\n专业建议：")
        for i, rec in enumerate(report['recommendations'], 1):
            print(f"  {i}. {rec}")
    
    return report


def save_results(analyzer, data):
    """保存分析结果"""
    print("\n=== 保存分析结果 ===")
    
    # 保存原始数据
    df_data = pd.DataFrame(data)
    df_data.to_csv('your_nyquist_data.csv', index=False)
    print("✓ 原始数据已保存为: your_nyquist_data.csv")
    
    # 保存拟合结果
    if CircuitModel.RANDLES in analyzer.fit_results:
        result = analyzer.fit_results[CircuitModel.RANDLES]
        if result.success:
            # 创建拟合结果DataFrame
            fit_data = {
                'freq': analyzer.data.frequency,
                'z_real_exp': analyzer.data.z_real,
                'z_imag_exp': analyzer.data.z_imag,
                'z_real_fit': result.fitted_impedance.real,
                'z_imag_fit': result.fitted_impedance.imag,
                'residual_real': analyzer.data.z_real - result.fitted_impedance.real,
                'residual_imag': analyzer.data.z_imag - result.fitted_impedance.imag
            }
            
            df_fit = pd.DataFrame(fit_data)
            df_fit.to_csv('nyquist_fit_results.csv', index=False)
            print("✓ 拟合结果已保存为: nyquist_fit_results.csv")
            
            # 保存参数
            params_df = pd.DataFrame([result.parameters])
            params_df.to_csv('extracted_parameters.csv', index=False)
            print("✓ 提取参数已保存为: extracted_parameters.csv")


def main():
    """主分析流程"""
    print("奈奎斯特图数据分析程序")
    print("基于您提供的图片数据进行分析")
    print("=" * 50)
    
    # 1. 分析数据
    analyzer, data = analyze_your_data()
    
    # 2. 绘制原始奈奎斯特图
    plot_comparison_with_your_image(analyzer)
    
    # 3. 等效电路拟合
    fit_result = fit_and_analyze(analyzer)
    
    # 4. 绘制拟合结果
    if fit_result.success:
        plot_fitted_nyquist(analyzer)
    
    # 5. 生成分析报告
    generate_analysis_report(analyzer)
    
    # 6. 保存结果
    save_results(analyzer, data)
    
    print("\n" + "=" * 50)
    print("分析完成！")
    print("\n如需分析您的实际数据：")
    print("1. 将数据保存为CSV格式，包含freq, z_real, z_imag列")
    print("2. 使用analyzer.load_data('your_file.csv')加载")
    print("3. 运行相同的分析流程")


if __name__ == "__main__":
    main()
