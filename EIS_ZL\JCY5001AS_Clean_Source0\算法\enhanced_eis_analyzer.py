#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版EIS电化学阻抗谱分析工具
功能包括：
1. 奈奎斯特图绘制和分析
2. 多种等效电路模型拟合
3. 参数自动提取和优化
4. 数据质量评估和预处理
5. 交互式可视化界面

作者：Jack
日期：2025-01-27
版本：v2.0
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.widgets import Slider, Button
import matplotlib.patches as patches
from scipy.optimize import curve_fit, minimize
from scipy import signal, interpolate
from scipy.stats import linregress
import warnings
from typing import Dict, List, Tuple, Optional, Union
import logging
from dataclasses import dataclass
from enum import Enum

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class CircuitModel(Enum):
    """等效电路模型枚举"""
    RANDLES = "randles"  # Randles电路：Rs + (Rct || CPE)
    DOUBLE_RC = "double_rc"  # 双RC电路：Rs + (R1||C1) + (R2||C2)
    WARBURG = "warburg"  # 含Warburg阻抗：Rs + (Rct || CPE) + W
    MODIFIED_RANDLES = "modified_randles"  # 修正Randles：Rs + (Rct || CPE) + L


@dataclass
class EISData:
    """EIS数据结构"""
    frequency: np.ndarray
    z_real: np.ndarray
    z_imag: np.ndarray
    z_magnitude: Optional[np.ndarray] = None
    phase: Optional[np.ndarray] = None
    
    def __post_init__(self):
        """计算衍生参数"""
        if self.z_magnitude is None:
            self.z_magnitude = np.sqrt(self.z_real**2 + self.z_imag**2)
        if self.phase is None:
            self.phase = np.arctan2(-self.z_imag, self.z_real) * 180 / np.pi


@dataclass
class FitResult:
    """拟合结果数据结构"""
    model: CircuitModel
    parameters: Dict[str, float]
    parameter_errors: Dict[str, float]
    rmse: float
    r_squared: float
    chi_squared: float
    fitted_impedance: np.ndarray
    success: bool
    message: str


class EnhancedEISAnalyzer:
    """增强版EIS分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.data: Optional[EISData] = None
        self.fit_results: Dict[CircuitModel, FitResult] = {}
        self.logger = logging.getLogger(__name__)
        self.logger.info("增强版EIS分析器初始化完成")
        
        # 绘图参数
        self.plot_config = {
            'figure_size': (12, 10),
            'dpi': 100,
            'line_width': 2,
            'marker_size': 6,
            'grid_alpha': 0.3,
            'colors': {
                'data': '#1f77b4',
                'fit': '#ff7f0e',
                'residual': '#d62728',
                'highlight': '#2ca02c'
            }
        }
    
    def load_data(self, data_source: Union[str, Dict, pd.DataFrame]) -> bool:
        """
        加载EIS数据
        
        Args:
            data_source: 数据源，可以是文件路径、字典或DataFrame
            
        Returns:
            bool: 加载是否成功
        """
        try:
            if isinstance(data_source, str):
                # 从文件加载
                if data_source.endswith('.csv'):
                    df = pd.read_csv(data_source)
                elif data_source.endswith(('.xlsx', '.xls')):
                    df = pd.read_excel(data_source)
                else:
                    raise ValueError(f"不支持的文件格式: {data_source}")
                    
            elif isinstance(data_source, dict):
                # 从字典加载
                df = pd.DataFrame(data_source)
                
            elif isinstance(data_source, pd.DataFrame):
                # 直接使用DataFrame
                df = data_source.copy()
                
            else:
                raise ValueError("不支持的数据源类型")
            
            # 标准化列名
            df = self._standardize_columns(df)
            
            # 验证必要列
            required_columns = ['freq', 'z_real', 'z_imag']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(f"缺少必要列: {missing_columns}")
            
            # 创建EISData对象
            self.data = EISData(
                frequency=df['freq'].values,
                z_real=df['z_real'].values,
                z_imag=df['z_imag'].values
            )
            
            # 数据质量检查
            quality_report = self._validate_data_quality()
            if not quality_report['is_valid']:
                self.logger.warning(f"数据质量问题: {quality_report['issues']}")
            
            self.logger.info(f"成功加载EIS数据: {len(self.data.frequency)}个数据点")
            self.logger.info(f"频率范围: {self.data.frequency.min():.2e} - {self.data.frequency.max():.2e} Hz")
            self.logger.info(f"阻抗范围: {self.data.z_real.min():.3f} - {self.data.z_real.max():.3f} Ω")
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据加载失败: {e}")
            return False
    
    def _standardize_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        标准化列名
        
        Args:
            df: 原始DataFrame
            
        Returns:
            pd.DataFrame: 标准化后的DataFrame
        """
        # 转换为小写
        df.columns = df.columns.str.lower().str.strip()
        
        # 列名映射
        column_mapping = {
            'frequency': 'freq',
            'freq_hz': 'freq',
            'f': 'freq',
            'real': 'z_real',
            'imag': 'z_imag',
            'impedance_real': 'z_real',
            'impedance_imag': 'z_imag',
            'z\'': 'z_real',
            'z\'\'': 'z_imag',
            'zreal': 'z_real',
            'zimag': 'z_imag',
            'phase': 'phase_deg',
            'magnitude': 'z_mag',
            'mag': 'z_mag'
        }
        
        # 应用映射
        df = df.rename(columns=column_mapping)
        
        return df
    
    def _validate_data_quality(self) -> Dict:
        """
        验证数据质量
        
        Returns:
            Dict: 数据质量报告
        """
        if self.data is None:
            return {'is_valid': False, 'issues': ['数据未加载']}
        
        issues = []
        
        # 检查数据点数量
        if len(self.data.frequency) < 5:
            issues.append('数据点数量过少（<5个）')
        
        # 检查频率范围
        freq_ratio = self.data.frequency.max() / self.data.frequency.min()
        if freq_ratio < 10:
            issues.append('频率范围过窄')
        
        # 检查数据有效性
        if np.any(~np.isfinite(self.data.z_real)) or np.any(~np.isfinite(self.data.z_imag)):
            issues.append('存在无效数据点（NaN或Inf）')
        
        # 检查物理合理性
        if np.any(self.data.z_real < 0):
            issues.append('存在负实部阻抗值')
        
        # 检查数据单调性（频率应该单调）
        if not (np.all(np.diff(self.data.frequency) > 0) or np.all(np.diff(self.data.frequency) < 0)):
            issues.append('频率数据非单调')
        
        return {
            'is_valid': len(issues) == 0,
            'issues': issues,
            'data_points': len(self.data.frequency),
            'frequency_range': freq_ratio,
            'impedance_range': {
                'real_min': self.data.z_real.min(),
                'real_max': self.data.z_real.max(),
                'imag_min': self.data.z_imag.min(),
                'imag_max': self.data.z_imag.max()
            }
        }

    def plot_nyquist(self, show_fit: bool = True, interactive: bool = False,
                    highlight_frequencies: Optional[List[float]] = None) -> plt.Figure:
        """
        绘制奈奎斯特图

        Args:
            show_fit: 是否显示拟合曲线
            interactive: 是否启用交互模式
            highlight_frequencies: 需要高亮显示的频率点

        Returns:
            plt.Figure: 图形对象
        """
        if self.data is None:
            raise ValueError("请先加载数据")

        # 创建图形
        fig, ax = plt.subplots(figsize=self.plot_config['figure_size'],
                              dpi=self.plot_config['dpi'])

        # 绘制实验数据
        scatter = ax.scatter(self.data.z_real, -self.data.z_imag,
                           c=np.log10(self.data.frequency),
                           cmap='viridis',
                           s=self.plot_config['marker_size']**2,
                           alpha=0.8,
                           edgecolors='black',
                           linewidth=0.5,
                           label='实验数据')

        # 添加颜色条
        cbar = plt.colorbar(scatter, ax=ax)
        cbar.set_label('log₁₀(频率/Hz)', fontsize=12)

        # 高亮特定频率点
        if highlight_frequencies:
            for freq in highlight_frequencies:
                # 找到最接近的频率点
                idx = np.argmin(np.abs(self.data.frequency - freq))
                ax.scatter(self.data.z_real[idx], -self.data.z_imag[idx],
                          color=self.plot_config['colors']['highlight'],
                          s=100, marker='*',
                          label=f'{freq:.1e} Hz', zorder=5)

        # 绘制拟合曲线
        if show_fit and self.fit_results:
            for model, result in self.fit_results.items():
                if result.success:
                    ax.plot(result.fitted_impedance.real,
                           -result.fitted_impedance.imag,
                           color=self.plot_config['colors']['fit'],
                           linewidth=self.plot_config['line_width'],
                           linestyle='--',
                           label=f'{model.value}拟合 (RMSE={result.rmse:.3f})')

        # 设置图形属性
        ax.set_xlabel('Z\' (Ω)', fontsize=14, fontweight='bold')
        ax.set_ylabel('-Z\'\' (Ω)', fontsize=14, fontweight='bold')
        ax.set_title('奈奎斯特图 (Nyquist Plot)', fontsize=16, fontweight='bold')
        ax.grid(True, alpha=self.plot_config['grid_alpha'])
        ax.legend(fontsize=10)

        # 设置等比例坐标轴
        ax.set_aspect('equal', adjustable='box')

        # 添加频率标注
        self._add_frequency_annotations(ax)

        # 交互模式
        if interactive:
            self._setup_interactive_nyquist(fig, ax)

        plt.tight_layout()
        return fig

    def _add_frequency_annotations(self, ax: plt.Axes, num_annotations: int = 5):
        """
        在奈奎斯特图上添加频率标注

        Args:
            ax: 坐标轴对象
            num_annotations: 标注数量
        """
        if self.data is None:
            return

        # 选择要标注的频率点（对数均匀分布）
        freq_log = np.log10(self.data.frequency)
        freq_indices = np.linspace(0, len(self.data.frequency)-1, num_annotations, dtype=int)

        for idx in freq_indices:
            freq = self.data.frequency[idx]
            x, y = self.data.z_real[idx], -self.data.z_imag[idx]

            # 格式化频率显示
            if freq >= 1e6:
                freq_str = f'{freq/1e6:.1f}M'
            elif freq >= 1e3:
                freq_str = f'{freq/1e3:.1f}k'
            elif freq >= 1:
                freq_str = f'{freq:.1f}'
            else:
                freq_str = f'{freq*1e3:.1f}m'

            # 添加标注
            ax.annotate(f'{freq_str}Hz',
                       xy=(x, y),
                       xytext=(5, 5),
                       textcoords='offset points',
                       fontsize=8,
                       alpha=0.7,
                       bbox=dict(boxstyle='round,pad=0.2',
                                facecolor='white',
                                alpha=0.7))

    def _setup_interactive_nyquist(self, fig: plt.Figure, ax: plt.Axes):
        """
        设置交互式奈奎斯特图

        Args:
            fig: 图形对象
            ax: 坐标轴对象
        """
        # 添加点击事件处理
        def on_click(event):
            if event.inaxes != ax:
                return

            # 找到最近的数据点
            if event.xdata is not None and event.ydata is not None:
                distances = np.sqrt((self.data.z_real - event.xdata)**2 +
                                  (-self.data.z_imag - event.ydata)**2)
                nearest_idx = np.argmin(distances)

                # 显示点信息
                freq = self.data.frequency[nearest_idx]
                z_real = self.data.z_real[nearest_idx]
                z_imag = self.data.z_imag[nearest_idx]
                z_mag = self.data.z_magnitude[nearest_idx]
                phase = self.data.phase[nearest_idx]

                info_text = (f'频率: {freq:.2e} Hz\n'
                           f'Z\': {z_real:.3f} Ω\n'
                           f'Z\'\': {z_imag:.3f} Ω\n'
                           f'|Z|: {z_mag:.3f} Ω\n'
                           f'相位: {phase:.1f}°')

                # 清除之前的标注
                for annotation in ax.texts:
                    if hasattr(annotation, '_is_info_text'):
                        annotation.remove()

                # 添加新标注
                annotation = ax.annotate(info_text,
                                       xy=(z_real, -z_imag),
                                       xytext=(20, 20),
                                       textcoords='offset points',
                                       bbox=dict(boxstyle='round,pad=0.5',
                                               facecolor='yellow',
                                               alpha=0.8),
                                       fontsize=10,
                                       ha='left')
                annotation._is_info_text = True
                fig.canvas.draw()

        fig.canvas.mpl_connect('button_press_event', on_click)

    def randles_circuit(self, frequency: np.ndarray, Rs: float, Rct: float,
                       CPE_T: float, CPE_n: float) -> np.ndarray:
        """
        Randles等效电路模型：Rs + (Rct || CPE)

        Args:
            frequency: 频率数组 (Hz)
            Rs: 溶液电阻 (Ω)
            Rct: 电荷转移电阻 (Ω)
            CPE_T: 常相位元件参数 (F·s^(n-1))
            CPE_n: 常相位元件指数 (0-1)

        Returns:
            np.ndarray: 复阻抗数组
        """
        omega = 2 * np.pi * frequency

        # CPE阻抗：Z_CPE = 1 / (T * (jω)^n)
        Z_CPE = 1 / (CPE_T * (1j * omega)**CPE_n)

        # 并联阻抗：Z_parallel = (Rct * Z_CPE) / (Rct + Z_CPE)
        Z_parallel = (Rct * Z_CPE) / (Rct + Z_CPE)

        # 总阻抗：Z_total = Rs + Z_parallel
        Z_total = Rs + Z_parallel

        return Z_total

    def double_rc_circuit(self, frequency: np.ndarray, Rs: float,
                         R1: float, C1: float, R2: float, C2: float) -> np.ndarray:
        """
        双RC等效电路模型：Rs + (R1||C1) + (R2||C2)

        Args:
            frequency: 频率数组 (Hz)
            Rs: 溶液电阻 (Ω)
            R1: 第一个电阻 (Ω)
            C1: 第一个电容 (F)
            R2: 第二个电阻 (Ω)
            C2: 第二个电容 (F)

        Returns:
            np.ndarray: 复阻抗数组
        """
        omega = 2 * np.pi * frequency

        # 第一个RC并联
        Z_C1 = 1 / (1j * omega * C1)
        Z_RC1 = (R1 * Z_C1) / (R1 + Z_C1)

        # 第二个RC并联
        Z_C2 = 1 / (1j * omega * C2)
        Z_RC2 = (R2 * Z_C2) / (R2 + Z_C2)

        # 总阻抗
        Z_total = Rs + Z_RC1 + Z_RC2

        return Z_total

    def warburg_circuit(self, frequency: np.ndarray, Rs: float, Rct: float,
                       CPE_T: float, CPE_n: float, W: float) -> np.ndarray:
        """
        含Warburg阻抗的等效电路：Rs + (Rct || CPE) + W

        Args:
            frequency: 频率数组 (Hz)
            Rs: 溶液电阻 (Ω)
            Rct: 电荷转移电阻 (Ω)
            CPE_T: 常相位元件参数 (F·s^(n-1))
            CPE_n: 常相位元件指数 (0-1)
            W: Warburg系数 (Ω·s^(-1/2))

        Returns:
            np.ndarray: 复阻抗数组
        """
        omega = 2 * np.pi * frequency

        # CPE阻抗
        Z_CPE = 1 / (CPE_T * (1j * omega)**CPE_n)

        # 并联阻抗
        Z_parallel = (Rct * Z_CPE) / (Rct + Z_CPE)

        # Warburg阻抗：Z_W = W / sqrt(jω)
        Z_warburg = W / np.sqrt(1j * omega)

        # 总阻抗
        Z_total = Rs + Z_parallel + Z_warburg

        return Z_total

    def fit_circuit_model(self, model: CircuitModel,
                         initial_guess: Optional[Dict[str, float]] = None,
                         bounds: Optional[Dict[str, Tuple[float, float]]] = None) -> FitResult:
        """
        拟合指定的等效电路模型

        Args:
            model: 电路模型类型
            initial_guess: 初始参数猜测值
            bounds: 参数边界

        Returns:
            FitResult: 拟合结果
        """
        if self.data is None:
            raise ValueError("请先加载数据")

        try:
            # 选择电路模型函数
            if model == CircuitModel.RANDLES:
                circuit_func = self.randles_circuit
                param_names = ['Rs', 'Rct', 'CPE_T', 'CPE_n']
                default_guess = self._get_randles_initial_guess()
                default_bounds = self._get_randles_bounds()

            elif model == CircuitModel.DOUBLE_RC:
                circuit_func = self.double_rc_circuit
                param_names = ['Rs', 'R1', 'C1', 'R2', 'C2']
                default_guess = self._get_double_rc_initial_guess()
                default_bounds = self._get_double_rc_bounds()

            elif model == CircuitModel.WARBURG:
                circuit_func = self.warburg_circuit
                param_names = ['Rs', 'Rct', 'CPE_T', 'CPE_n', 'W']
                default_guess = self._get_warburg_initial_guess()
                default_bounds = self._get_warburg_bounds()

            else:
                raise ValueError(f"不支持的电路模型: {model}")

            # 使用提供的参数或默认值
            guess = initial_guess if initial_guess else default_guess
            param_bounds = bounds if bounds else default_bounds

            # 准备拟合数据
            freq = self.data.frequency
            z_complex = self.data.z_real + 1j * self.data.z_imag

            # 定义目标函数
            def objective_function(params):
                z_model = circuit_func(freq, *params)
                residuals = z_complex - z_model
                # 使用加权最小二乘，权重为阻抗幅值的倒数
                weights = 1 / np.abs(z_complex)
                weighted_residuals = residuals * weights
                return np.concatenate([weighted_residuals.real, weighted_residuals.imag])

            # 转换参数格式
            p0 = [guess[name] for name in param_names]
            lower_bounds = [param_bounds[name][0] for name in param_names]
            upper_bounds = [param_bounds[name][1] for name in param_names]

            # 执行拟合
            result = minimize(
                lambda params: np.sum(objective_function(params)**2),
                p0,
                bounds=list(zip(lower_bounds, upper_bounds)),
                method='L-BFGS-B'
            )

            if result.success:
                # 计算拟合质量指标
                fitted_params = dict(zip(param_names, result.x))
                z_fitted = circuit_func(freq, *result.x)

                # 计算误差指标
                residuals = z_complex - z_fitted
                rmse = np.sqrt(np.mean(np.abs(residuals)**2))

                # R²计算
                ss_res = np.sum(np.abs(residuals)**2)
                ss_tot = np.sum(np.abs(z_complex - np.mean(z_complex))**2)
                r_squared = 1 - (ss_res / ss_tot)

                # 卡方检验
                chi_squared = np.sum((np.abs(residuals) / np.abs(z_complex))**2)

                # 参数误差估计（简化版）
                param_errors = self._estimate_parameter_errors(result, param_names)

                fit_result = FitResult(
                    model=model,
                    parameters=fitted_params,
                    parameter_errors=param_errors,
                    rmse=rmse,
                    r_squared=r_squared,
                    chi_squared=chi_squared,
                    fitted_impedance=z_fitted,
                    success=True,
                    message="拟合成功"
                )

                self.fit_results[model] = fit_result
                self.logger.info(f"{model.value}模型拟合成功: RMSE={rmse:.4f}, R²={r_squared:.4f}")

                return fit_result

            else:
                error_msg = f"拟合失败: {result.message}"
                self.logger.error(error_msg)

                return FitResult(
                    model=model,
                    parameters={},
                    parameter_errors={},
                    rmse=float('inf'),
                    r_squared=0.0,
                    chi_squared=float('inf'),
                    fitted_impedance=np.array([]),
                    success=False,
                    message=error_msg
                )

        except Exception as e:
            error_msg = f"拟合过程异常: {str(e)}"
            self.logger.error(error_msg)

            return FitResult(
                model=model,
                parameters={},
                parameter_errors={},
                rmse=float('inf'),
                r_squared=0.0,
                chi_squared=float('inf'),
                fitted_impedance=np.array([]),
                success=False,
                message=error_msg
            )

    def _get_randles_initial_guess(self) -> Dict[str, float]:
        """获取Randles电路的初始参数猜测"""
        if self.data is None:
            return {'Rs': 10.0, 'Rct': 50.0, 'CPE_T': 1e-5, 'CPE_n': 0.8}

        # 智能初始猜测
        Rs_guess = np.min(self.data.z_real)  # 高频实部作为Rs
        Rct_guess = np.max(self.data.z_real) - Rs_guess  # 低频-高频差值作为Rct

        # CPE参数的经验估计
        max_imag_idx = np.argmax(np.abs(self.data.z_imag))
        freq_at_max_imag = self.data.frequency[max_imag_idx]
        CPE_T_guess = 1 / (2 * np.pi * freq_at_max_imag * Rct_guess) if Rct_guess > 0 else 1e-5
        CPE_n_guess = 0.85  # 典型值

        return {
            'Rs': max(0.1, Rs_guess),
            'Rct': max(1.0, Rct_guess),
            'CPE_T': max(1e-8, min(1e-2, CPE_T_guess)),
            'CPE_n': CPE_n_guess
        }

    def _get_randles_bounds(self) -> Dict[str, Tuple[float, float]]:
        """获取Randles电路的参数边界"""
        return {
            'Rs': (0.01, 1000.0),
            'Rct': (0.1, 10000.0),
            'CPE_T': (1e-10, 1e-1),
            'CPE_n': (0.5, 1.0)
        }

    def _get_double_rc_initial_guess(self) -> Dict[str, float]:
        """获取双RC电路的初始参数猜测"""
        if self.data is None:
            return {'Rs': 10.0, 'R1': 20.0, 'C1': 1e-5, 'R2': 30.0, 'C2': 1e-3}

        Rs_guess = np.min(self.data.z_real)
        total_R = np.max(self.data.z_real)

        # 假设两个RC时间常数相差一个数量级
        R1_guess = (total_R - Rs_guess) * 0.3
        R2_guess = (total_R - Rs_guess) * 0.7

        # 根据频率范围估计电容
        freq_range = self.data.frequency
        C1_guess = 1 / (2 * np.pi * np.max(freq_range) * R1_guess) if R1_guess > 0 else 1e-6
        C2_guess = 1 / (2 * np.pi * np.min(freq_range) * R2_guess) if R2_guess > 0 else 1e-4

        return {
            'Rs': max(0.1, Rs_guess),
            'R1': max(1.0, R1_guess),
            'C1': max(1e-8, C1_guess),
            'R2': max(1.0, R2_guess),
            'C2': max(1e-8, C2_guess)
        }

    def _get_double_rc_bounds(self) -> Dict[str, Tuple[float, float]]:
        """获取双RC电路的参数边界"""
        return {
            'Rs': (0.01, 1000.0),
            'R1': (0.1, 10000.0),
            'C1': (1e-10, 1e-1),
            'R2': (0.1, 10000.0),
            'C2': (1e-10, 1e-1)
        }

    def _get_warburg_initial_guess(self) -> Dict[str, float]:
        """获取Warburg电路的初始参数猜测"""
        randles_guess = self._get_randles_initial_guess()

        # Warburg系数的经验估计
        if self.data is not None:
            # 在低频区域寻找45度线的斜率
            low_freq_mask = self.data.frequency < np.median(self.data.frequency)
            if np.any(low_freq_mask):
                low_freq_real = self.data.z_real[low_freq_mask]
                low_freq_imag = self.data.z_imag[low_freq_mask]

                # 简单的线性拟合估计Warburg系数
                if len(low_freq_real) > 2:
                    slope, _, _, _, _ = linregress(low_freq_real, -low_freq_imag)
                    W_guess = abs(slope) * 10  # 经验调整
                else:
                    W_guess = 10.0
            else:
                W_guess = 10.0
        else:
            W_guess = 10.0

        return {
            **randles_guess,
            'W': max(0.1, W_guess)
        }

    def _get_warburg_bounds(self) -> Dict[str, Tuple[float, float]]:
        """获取Warburg电路的参数边界"""
        randles_bounds = self._get_randles_bounds()
        return {
            **randles_bounds,
            'W': (0.01, 1000.0)
        }

    def _estimate_parameter_errors(self, fit_result, param_names: List[str]) -> Dict[str, float]:
        """
        估计参数误差（简化版本）

        Args:
            fit_result: 优化结果
            param_names: 参数名称列表

        Returns:
            Dict[str, float]: 参数误差字典
        """
        # 简化的误差估计，实际应用中可以使用更精确的方法
        # 如Hessian矩阵的逆矩阵对角元素的平方根

        errors = {}
        for i, name in enumerate(param_names):
            # 使用参数值的5%作为误差估计（简化方法）
            param_value = fit_result.x[i]
            errors[name] = abs(param_value) * 0.05

        return errors

    def compare_models(self) -> pd.DataFrame:
        """
        比较不同模型的拟合效果

        Returns:
            pd.DataFrame: 模型比较结果
        """
        if not self.fit_results:
            self.logger.warning("没有拟合结果可比较")
            return pd.DataFrame()

        comparison_data = []

        for model, result in self.fit_results.items():
            if result.success:
                row = {
                    '模型': model.value,
                    'RMSE': result.rmse,
                    'R²': result.r_squared,
                    'χ²': result.chi_squared,
                    '参数数量': len(result.parameters),
                    '拟合状态': '成功' if result.success else '失败'
                }

                # 添加主要参数
                if 'Rs' in result.parameters:
                    row['Rs (Ω)'] = f"{result.parameters['Rs']:.3f}"
                if 'Rct' in result.parameters:
                    row['Rct (Ω)'] = f"{result.parameters['Rct']:.3f}"
                if 'CPE_T' in result.parameters:
                    row['CPE_T'] = f"{result.parameters['CPE_T']:.2e}"
                if 'CPE_n' in result.parameters:
                    row['CPE_n'] = f"{result.parameters['CPE_n']:.3f}"

                comparison_data.append(row)

        df = pd.DataFrame(comparison_data)

        # 按RMSE排序
        if not df.empty:
            df = df.sort_values('RMSE')

        return df

    def plot_bode(self, show_fit: bool = True) -> plt.Figure:
        """
        绘制Bode图（幅值和相位）

        Args:
            show_fit: 是否显示拟合曲线

        Returns:
            plt.Figure: 图形对象
        """
        if self.data is None:
            raise ValueError("请先加载数据")

        # 创建子图
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

        # 幅值图
        ax1.loglog(self.data.frequency, self.data.z_magnitude,
                  'o-', color=self.plot_config['colors']['data'],
                  markersize=self.plot_config['marker_size'],
                  linewidth=self.plot_config['line_width'],
                  label='实验数据')

        # 相位图
        ax2.semilogx(self.data.frequency, self.data.phase,
                    'o-', color=self.plot_config['colors']['data'],
                    markersize=self.plot_config['marker_size'],
                    linewidth=self.plot_config['line_width'],
                    label='实验数据')

        # 绘制拟合曲线
        if show_fit and self.fit_results:
            for model, result in self.fit_results.items():
                if result.success:
                    fit_magnitude = np.abs(result.fitted_impedance)
                    fit_phase = np.angle(result.fitted_impedance) * 180 / np.pi

                    ax1.loglog(self.data.frequency, fit_magnitude,
                              '--', color=self.plot_config['colors']['fit'],
                              linewidth=self.plot_config['line_width'],
                              label=f'{model.value}拟合')

                    ax2.semilogx(self.data.frequency, fit_phase,
                                '--', color=self.plot_config['colors']['fit'],
                                linewidth=self.plot_config['line_width'],
                                label=f'{model.value}拟合')

        # 设置图形属性
        ax1.set_xlabel('频率 (Hz)', fontsize=12)
        ax1.set_ylabel('|Z| (Ω)', fontsize=12)
        ax1.set_title('阻抗幅值 vs 频率', fontsize=14, fontweight='bold')
        ax1.grid(True, alpha=self.plot_config['grid_alpha'])
        ax1.legend()

        ax2.set_xlabel('频率 (Hz)', fontsize=12)
        ax2.set_ylabel('相位角 (°)', fontsize=12)
        ax2.set_title('相位角 vs 频率', fontsize=14, fontweight='bold')
        ax2.grid(True, alpha=self.plot_config['grid_alpha'])
        ax2.legend()

        plt.tight_layout()
        return fig

    def plot_residuals(self, model: CircuitModel) -> plt.Figure:
        """
        绘制拟合残差图

        Args:
            model: 要分析的模型

        Returns:
            plt.Figure: 图形对象
        """
        if model not in self.fit_results:
            raise ValueError(f"模型 {model.value} 未进行拟合")

        result = self.fit_results[model]
        if not result.success:
            raise ValueError(f"模型 {model.value} 拟合失败")

        # 计算残差
        residuals = (self.data.z_real + 1j * self.data.z_imag) - result.fitted_impedance
        residual_magnitude = np.abs(residuals)
        residual_phase = np.angle(residuals) * 180 / np.pi

        # 创建子图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

        # 残差幅值 vs 频率
        ax1.semilogx(self.data.frequency, residual_magnitude,
                    'o-', color=self.plot_config['colors']['residual'],
                    markersize=self.plot_config['marker_size'])
        ax1.set_xlabel('频率 (Hz)')
        ax1.set_ylabel('残差幅值 (Ω)')
        ax1.set_title('残差幅值 vs 频率')
        ax1.grid(True, alpha=self.plot_config['grid_alpha'])

        # 残差相位 vs 频率
        ax2.semilogx(self.data.frequency, residual_phase,
                    'o-', color=self.plot_config['colors']['residual'],
                    markersize=self.plot_config['marker_size'])
        ax2.set_xlabel('频率 (Hz)')
        ax2.set_ylabel('残差相位 (°)')
        ax2.set_title('残差相位 vs 频率')
        ax2.grid(True, alpha=self.plot_config['grid_alpha'])

        # 实部残差 vs 虚部残差
        ax3.scatter(residuals.real, residuals.imag,
                   c=np.log10(self.data.frequency), cmap='viridis',
                   s=self.plot_config['marker_size']**2)
        ax3.set_xlabel('实部残差 (Ω)')
        ax3.set_ylabel('虚部残差 (Ω)')
        ax3.set_title('残差分布图')
        ax3.grid(True, alpha=self.plot_config['grid_alpha'])
        ax3.axhline(y=0, color='k', linestyle='--', alpha=0.5)
        ax3.axvline(x=0, color='k', linestyle='--', alpha=0.5)

        # 残差直方图
        ax4.hist(residual_magnitude, bins=20, alpha=0.7,
                color=self.plot_config['colors']['residual'])
        ax4.set_xlabel('残差幅值 (Ω)')
        ax4.set_ylabel('频次')
        ax4.set_title('残差分布直方图')
        ax4.grid(True, alpha=self.plot_config['grid_alpha'])

        plt.suptitle(f'{model.value}模型残差分析', fontsize=16, fontweight='bold')
        plt.tight_layout()
        return fig

    def generate_analysis_report(self) -> Dict:
        """
        生成综合分析报告

        Returns:
            Dict: 分析报告
        """
        if self.data is None:
            return {'error': '数据未加载'}

        report = {
            'data_info': {
                'data_points': len(self.data.frequency),
                'frequency_range': {
                    'min': float(self.data.frequency.min()),
                    'max': float(self.data.frequency.max()),
                    'ratio': float(self.data.frequency.max() / self.data.frequency.min())
                },
                'impedance_range': {
                    'real_min': float(self.data.z_real.min()),
                    'real_max': float(self.data.z_real.max()),
                    'imag_min': float(self.data.z_imag.min()),
                    'imag_max': float(self.data.z_imag.max()),
                    'magnitude_min': float(self.data.z_magnitude.min()),
                    'magnitude_max': float(self.data.z_magnitude.max())
                }
            },
            'data_quality': self._validate_data_quality(),
            'fit_results': {},
            'recommendations': []
        }

        # 添加拟合结果
        for model, result in self.fit_results.items():
            if result.success:
                report['fit_results'][model.value] = {
                    'parameters': result.parameters,
                    'parameter_errors': result.parameter_errors,
                    'quality_metrics': {
                        'rmse': result.rmse,
                        'r_squared': result.r_squared,
                        'chi_squared': result.chi_squared
                    },
                    'message': result.message
                }

        # 生成建议
        report['recommendations'] = self._generate_recommendations()

        return report

    def _generate_recommendations(self) -> List[str]:
        """生成分析建议"""
        recommendations = []

        if self.data is None:
            return recommendations

        # 数据质量建议
        quality = self._validate_data_quality()
        if not quality['is_valid']:
            recommendations.extend([f"数据质量问题: {issue}" for issue in quality['issues']])

        # 频率范围建议
        freq_ratio = self.data.frequency.max() / self.data.frequency.min()
        if freq_ratio < 100:
            recommendations.append("建议扩大频率测量范围以获得更准确的参数")

        # 数据点数量建议
        if len(self.data.frequency) < 20:
            recommendations.append("建议增加测量点数以提高拟合精度")

        # 拟合模型建议
        if self.fit_results:
            best_model = min(self.fit_results.items(),
                           key=lambda x: x[1].rmse if x[1].success else float('inf'))
            if best_model[1].success:
                recommendations.append(f"推荐使用{best_model[0].value}模型，拟合效果最佳")

                # 参数合理性检查
                params = best_model[1].parameters
                if 'Rs' in params and params['Rs'] < 0.1:
                    recommendations.append("Rs值偏小，可能存在测量误差或接触电阻问题")
                if 'Rct' in params and params['Rct'] > 1000:
                    recommendations.append("Rct值偏大，可能表明电极反应动力学较慢")

        return recommendations


# 使用示例和测试代码
if __name__ == "__main__":
    print("增强版EIS分析工具已准备就绪")
    print("主要功能：")
    print("1. 数据加载和验证")
    print("2. 奈奎斯特图绘制")
    print("3. 等效电路拟合")
    print("4. 参数提取和分析")
    print("5. 交互式可视化")
    print("6. Bode图绘制")
    print("7. 残差分析")
    print("8. 综合报告生成")

    # 创建示例数据进行测试
    analyzer = EnhancedEISAnalyzer()

    # 生成示例EIS数据（Randles电路）
    freq = np.logspace(-2, 6, 50)  # 0.01 Hz to 1 MHz
    omega = 2 * np.pi * freq

    # 示例参数
    Rs = 10.0  # 溶液电阻 (Ω)
    Rct = 50.0  # 电荷转移电阻 (Ω)
    CPE_T = 1e-5  # CPE参数 (F·s^(n-1))
    CPE_n = 0.85  # CPE指数

    # 计算Randles电路阻抗
    Z_CPE = 1 / (CPE_T * (1j * omega)**CPE_n)
    Z_parallel = (Rct * Z_CPE) / (Rct + Z_CPE)
    Z_total = Rs + Z_parallel

    # 添加少量噪声
    noise_level = 0.02
    Z_total += noise_level * (np.random.randn(len(freq)) + 1j * np.random.randn(len(freq)))

    # 创建测试数据
    test_data = {
        'freq': freq,
        'z_real': Z_total.real,
        'z_imag': Z_total.imag
    }

    # 加载测试数据
    if analyzer.load_data(test_data):
        print("\n✓ 测试数据加载成功！")
        quality_report = analyzer._validate_data_quality()
        print(f"数据质量: {'良好' if quality_report['is_valid'] else '有问题'}")

        # 测试Randles电路拟合
        print("\n正在拟合Randles电路...")
        randles_result = analyzer.fit_circuit_model(CircuitModel.RANDLES)

        if randles_result.success:
            print("✓ Randles电路拟合成功！")
            print(f"  Rs = {randles_result.parameters['Rs']:.3f} Ω")
            print(f"  Rct = {randles_result.parameters['Rct']:.3f} Ω")
            print(f"  CPE_T = {randles_result.parameters['CPE_T']:.2e} F·s^(n-1)")
            print(f"  CPE_n = {randles_result.parameters['CPE_n']:.3f}")
            print(f"  RMSE = {randles_result.rmse:.4f}")
            print(f"  R² = {randles_result.r_squared:.4f}")
        else:
            print("✗ Randles电路拟合失败")

        # 测试双RC电路拟合
        print("\n正在拟合双RC电路...")
        double_rc_result = analyzer.fit_circuit_model(CircuitModel.DOUBLE_RC)

        if double_rc_result.success:
            print("✓ 双RC电路拟合成功！")
            print(f"  RMSE = {double_rc_result.rmse:.4f}")
            print(f"  R² = {double_rc_result.r_squared:.4f}")
        else:
            print("✗ 双RC电路拟合失败")

        # 模型比较
        print("\n模型比较结果：")
        comparison = analyzer.compare_models()
        if not comparison.empty:
            print(comparison.to_string(index=False))

        # 生成分析报告
        print("\n生成综合分析报告...")
        report = analyzer.generate_analysis_report()

        print(f"\n数据信息：")
        print(f"  数据点数: {report['data_info']['data_points']}")
        print(f"  频率范围: {report['data_info']['frequency_range']['min']:.2e} - {report['data_info']['frequency_range']['max']:.2e} Hz")

        if report['recommendations']:
            print(f"\n建议：")
            for i, rec in enumerate(report['recommendations'], 1):
                print(f"  {i}. {rec}")

        print("\n使用示例：")
        print("# 绘制奈奎斯特图")
        print("fig_nyquist = analyzer.plot_nyquist(interactive=True)")
        print("plt.show()")
        print("\n# 绘制Bode图")
        print("fig_bode = analyzer.plot_bode()")
        print("plt.show()")
        print("\n# 绘制残差图")
        print("fig_residuals = analyzer.plot_residuals(CircuitModel.RANDLES)")
        print("plt.show()")

    else:
        print("✗ 测试数据加载失败！")
