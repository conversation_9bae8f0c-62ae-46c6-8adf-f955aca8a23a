<?xml version="1.0" encoding="UTF-8"?>
<svg width="1000" height="710" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; text-anchor: middle; }
      .axis-label { font-family: Arial, sans-serif; font-size: 14px; text-anchor: middle; font-weight: bold; }
      .tick-label { font-family: Arial, sans-serif; font-size: 12px; text-anchor: middle; }
      .grid-line { stroke: #e0e0e0; stroke-width: 0.8; }
      .axis-line { stroke: #000; stroke-width: 2; }
      .curve1 { stroke: #1f77b4; stroke-width: 2.5; fill: none; }
      .curve2 { stroke: #ff7f0e; stroke-width: 2.5; fill: none; }
      .curve3 { stroke: #2ca02c; stroke-width: 2.5; fill: none; }
      .curve4 { stroke: #d62728; stroke-width: 2.5; fill: none; }
      .curve5 { stroke: #9467bd; stroke-width: 2.5; fill: none; }
      .curve6 { stroke: #8c564b; stroke-width: 2.5; fill: none; }
      .curve7 { stroke: #e377c2; stroke-width: 2.5; fill: none; }
      .curve8 { stroke: #7f7f7f; stroke-width: 2.5; fill: none; }
      .curve9 { stroke: #bcbd22; stroke-width: 2.5; fill: none; }
      .curve10 { stroke: #17becf; stroke-width: 2.5; fill: none; }
      .curve11 { stroke: #000000; stroke-width: 2.5; fill: none; }
    </style>
  </defs>
  
  <!-- 白色背景 -->
  <rect x="0" y="0" width="1000" height="710" fill="white"/>
  
  <!-- 标题 -->
  <text x="500" y="30" class="title">奈奎斯特图</text>
  
  <!-- 坐标轴 -->
  <!-- X轴 -->
  <line x1="100" y1="400" x2="900" y2="400" class="axis-line"/>
  <text x="500" y="440" class="axis-label">实部 Z' (μΩ)</text>
  
  <!-- Y轴 -->
  <line x1="100" y1="600" x2="100" y2="100" class="axis-line"/>
  <text x="40" y="350" class="axis-label" transform="rotate(-90, 40, 350)">虚部 Z'' (μΩ)</text>
  
  <!-- 网格线和刻度 -->
  <!-- X轴刻度 (13000-19000 μΩ) -->
  <g class="grid-line">
    <line x1="100" y1="395" x2="100" y2="405"/>
    <text x="100" y="420" class="tick-label">13000</text>
    <line x1="100" y1="400" x2="100" y2="100"/>
    
    <line x1="233" y1="395" x2="233" y2="405"/>
    <text x="233" y="420" class="tick-label">14000</text>
    <line x1="233" y1="400" x2="233" y2="100"/>
    
    <line x1="367" y1="395" x2="367" y2="405"/>
    <text x="367" y="420" class="tick-label">15000</text>
    <line x1="367" y1="400" x2="367" y2="100"/>
    
    <line x1="500" y1="395" x2="500" y2="405"/>
    <text x="500" y="420" class="tick-label">16000</text>
    <line x1="500" y1="400" x2="500" y2="100"/>
    
    <line x1="633" y1="395" x2="633" y2="405"/>
    <text x="633" y="420" class="tick-label">17000</text>
    <line x1="633" y1="400" x2="633" y2="100"/>
    
    <line x1="767" y1="395" x2="767" y2="405"/>
    <text x="767" y="420" class="tick-label">18000</text>
    <line x1="767" y1="400" x2="767" y2="100"/>
    
    <line x1="900" y1="395" x2="900" y2="405"/>
    <text x="900" y="420" class="tick-label">19000</text>
  </g>
  
  <!-- Y轴刻度 (-1500 到 1000 μΩ) -->
  <g class="grid-line">
    <!-- 1000线 -->
    <line x1="95" y1="160" x2="105" y2="160"/>
    <text x="85" y="165" class="tick-label">1000</text>
    <line x1="100" y1="160" x2="900" y2="160"/>
    
    <!-- 500线 -->
    <line x1="95" y1="240" x2="105" y2="240"/>
    <text x="85" y="245" class="tick-label">500</text>
    <line x1="100" y1="240" x2="900" y2="240"/>
    
    <!-- 0线 (中心线) -->
    <line x1="95" y1="400" x2="105" y2="400"/>
    <text x="85" y="405" class="tick-label">0</text>
    <line x1="100" y1="400" x2="900" y2="400" stroke-dasharray="2,2"/>
    
    <!-- -500线 -->
    <line x1="95" y1="480" x2="105" y2="480"/>
    <text x="85" y="485" class="tick-label">-500</text>
    <line x1="100" y1="480" x2="900" y2="480"/>
    
    <!-- -1000线 -->
    <line x1="95" y1="560" x2="105" y2="560"/>
    <text x="85" y="565" class="tick-label">-1000</text>
    <line x1="100" y1="560" x2="900" y2="560"/>
    
    <!-- -1500线 -->
    <line x1="95" y1="600" x2="105" y2="600"/>
    <text x="85" y="605" class="tick-label">-1500</text>
    <line x1="100" y1="600" x2="900" y2="600"/>
  </g>
  
  <!-- 多条奈奎斯特曲线 - 模拟您参考图中的多条彩色曲线 -->
  
  <!-- 曲线1 - 蓝色 -->
  <path d="M 100 600 Q 200 500 300 450 Q 400 400 500 420 Q 600 440 700 480 Q 800 520 850 560"
        class="curve1"/>
  
  <!-- 曲线2 - 橙色 -->
  <path d="M 120 580 Q 220 480 320 430 Q 420 380 520 400 Q 620 420 720 460 Q 820 500 870 540"
        class="curve2"/>
  
  <!-- 曲线3 - 绿色 -->
  <path d="M 140 560 Q 240 460 340 410 Q 440 360 540 380 Q 640 400 740 440 Q 840 480 890 520"
        class="curve3"/>
  
  <!-- 曲线4 - 红色 -->
  <path d="M 160 540 Q 260 440 360 390 Q 460 340 560 360 Q 660 380 760 420 Q 860 460 900 500"
        class="curve4"/>
  
  <!-- 曲线5 - 紫色 -->
  <path d="M 180 520 Q 280 420 380 370 Q 480 320 580 340 Q 680 360 780 400 Q 880 440 920 480"
        class="curve5"/>
  
  <!-- 曲线6 - 棕色 -->
  <path d="M 200 500 Q 300 400 400 350 Q 500 300 600 320 Q 700 340 800 380 Q 900 420 940 460"
        class="curve6"/>
  
  <!-- 曲线7 - 粉色 -->
  <path d="M 220 480 Q 320 380 420 330 Q 520 280 620 300 Q 720 320 820 360 Q 920 400 960 440"
        class="curve7"/>
  
  <!-- 曲线8 - 灰色 -->
  <path d="M 240 460 Q 340 360 440 310 Q 540 260 640 280 Q 740 300 840 340 Q 940 380 980 420"
        class="curve8"/>
  
  <!-- 曲线9 - 黄绿色 -->
  <path d="M 260 440 Q 360 340 460 290 Q 560 240 660 260 Q 760 280 860 320 Q 960 360 1000 400"
        class="curve9"/>
  
  <!-- 曲线10 - 青色 -->
  <path d="M 280 420 Q 380 320 480 270 Q 580 220 680 240 Q 780 260 880 300 Q 980 340 1020 380"
        class="curve10"/>
  
  <!-- 曲线11 - 黑色 (最突出的一条) -->
  <path d="M 300 400 Q 400 300 500 250 Q 600 200 700 220 Q 800 240 900 280 Q 1000 320 1050 360"
        class="curve11"/>
  
  <!-- 添加一些数据点标记 -->
  <g fill="#333" opacity="0.7">
    <circle cx="150" cy="580" r="2"/>
    <circle cx="200" cy="520" r="2"/>
    <circle cx="300" cy="450" r="2"/>
    <circle cx="400" cy="400" r="2"/>
    <circle cx="500" cy="380" r="2"/>
    <circle cx="600" cy="400" r="2"/>
    <circle cx="700" cy="440" r="2"/>
    <circle cx="800" cy="500" r="2"/>
  </g>
  
</svg>
