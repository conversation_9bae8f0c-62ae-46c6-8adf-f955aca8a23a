<?xml version="1.0" encoding="UTF-8"?>
<svg width="1000" height="710" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; text-anchor: middle; }
      .axis-label { font-family: Arial, sans-serif; font-size: 14px; text-anchor: middle; font-weight: bold; }
      .tick-label { font-family: Arial, sans-serif; font-size: 12px; text-anchor: middle; }
      .grid-line { stroke: #e0e0e0; stroke-width: 0.8; }
      .axis-line { stroke: #000; stroke-width: 2; }
      .curve1 { stroke: #1f77b4; stroke-width: 2.5; fill: none; }
      .curve2 { stroke: #ff7f0e; stroke-width: 2.5; fill: none; }
      .curve3 { stroke: #2ca02c; stroke-width: 2.5; fill: none; }
      .curve4 { stroke: #d62728; stroke-width: 2.5; fill: none; }
      .curve5 { stroke: #9467bd; stroke-width: 2.5; fill: none; }
      .curve6 { stroke: #8c564b; stroke-width: 2.5; fill: none; }
      .curve7 { stroke: #e377c2; stroke-width: 2.5; fill: none; }
      .curve8 { stroke: #7f7f7f; stroke-width: 2.5; fill: none; }
      .curve9 { stroke: #bcbd22; stroke-width: 2.5; fill: none; }
      .curve10 { stroke: #17becf; stroke-width: 2.5; fill: none; }
      .curve11 { stroke: #000000; stroke-width: 2.5; fill: none; }
    </style>
  </defs>
  
  <!-- 白色背景 -->
  <rect x="0" y="0" width="1000" height="710" fill="white"/>
  
  <!-- 标题 -->
  <text x="500" y="30" class="title">图5 典型电池奈奎斯特曲线示例图</text>

  <!-- 坐标轴 -->
  <!-- X轴 -->
  <line x1="100" y1="550" x2="850" y2="550" class="axis-line" marker-end="url(#arrowhead)"/>
  <text x="475" y="575" class="axis-label">实部阻抗 Z' (mΩ)</text>

  <!-- Y轴 -->
  <line x1="100" y1="550" x2="100" y2="100" class="axis-line" marker-end="url(#arrowhead)"/>
  <text x="50" y="325" class="axis-label" transform="rotate(-90, 50, 325)">虚部阻抗 -Z'' (mΩ)</text>

  <!-- 网格线和刻度 -->
  <!-- X轴刻度 -->
  <g class="grid-line">
    <line x1="200" y1="545" x2="200" y2="555"/>
    <text x="200" y="570" class="tick-label">10</text>
    <line x1="200" y1="550" x2="200" y2="100"/>

    <line x1="300" y1="545" x2="300" y2="555"/>
    <text x="300" y="570" class="tick-label">20</text>
    <line x1="300" y1="550" x2="300" y2="100"/>

    <line x1="400" y1="545" x2="400" y2="555"/>
    <text x="400" y="570" class="tick-label">30</text>
    <line x1="400" y1="550" x2="400" y2="100"/>

    <line x1="500" y1="545" x2="500" y2="555"/>
    <text x="500" y="570" class="tick-label">40</text>
    <line x1="500" y1="550" x2="500" y2="100"/>

    <line x1="600" y1="545" x2="600" y2="555"/>
    <text x="600" y="570" class="tick-label">50</text>
    <line x1="600" y1="550" x2="600" y2="100"/>

    <line x1="700" y1="545" x2="700" y2="555"/>
    <text x="700" y="570" class="tick-label">60</text>
    <line x1="700" y1="550" x2="700" y2="100"/>
  </g>

  <!-- Y轴刻度 -->
  <g class="grid-line">
    <line x1="95" y1="500" x2="105" y2="500"/>
    <text x="85" y="505" class="tick-label">5</text>
    <line x1="100" y1="500" x2="850" y2="500"/>

    <line x1="95" y1="450" x2="105" y2="450"/>
    <text x="85" y="455" class="tick-label">10</text>
    <line x1="100" y1="450" x2="850" y2="450"/>

    <line x1="95" y1="400" x2="105" y2="400"/>
    <text x="85" y="405" class="tick-label">15</text>
    <line x1="100" y1="400" x2="850" y2="400"/>

    <line x1="95" y1="350" x2="105" y2="350"/>
    <text x="85" y="355" class="tick-label">20</text>
    <line x1="100" y1="350" x2="850" y2="350"/>

    <line x1="95" y1="300" x2="105" y2="300"/>
    <text x="85" y="305" class="tick-label">25</text>
    <line x1="100" y1="300" x2="850" y2="300"/>
  </g>
  
  <!-- 多条奈奎斯特曲线 - 模拟您参考图中的多条彩色曲线 -->
  
  <!-- 曲线1 - 蓝色 -->
  <path d="M 100 600 Q 200 500 300 450 Q 400 400 500 420 Q 600 440 700 480 Q 800 520 850 560"
        class="curve1"/>
  
  <!-- 曲线2 - 橙色 -->
  <path d="M 120 580 Q 220 480 320 430 Q 420 380 520 400 Q 620 420 720 460 Q 820 500 870 540"
        class="curve2"/>
  
  <!-- 曲线3 - 绿色 -->
  <path d="M 140 560 Q 240 460 340 410 Q 440 360 540 380 Q 640 400 740 440 Q 840 480 890 520"
        class="curve3"/>
  
  <!-- 曲线4 - 红色 -->
  <path d="M 160 540 Q 260 440 360 390 Q 460 340 560 360 Q 660 380 760 420 Q 860 460 900 500"
        class="curve4"/>
  
  <!-- 曲线5 - 紫色 -->
  <path d="M 180 520 Q 280 420 380 370 Q 480 320 580 340 Q 680 360 780 400 Q 880 440 920 480"
        class="curve5"/>
  
  <!-- 曲线6 - 棕色 -->
  <path d="M 200 500 Q 300 400 400 350 Q 500 300 600 320 Q 700 340 800 380 Q 900 420 940 460"
        class="curve6"/>
  
  <!-- 曲线7 - 粉色 -->
  <path d="M 220 480 Q 320 380 420 330 Q 520 280 620 300 Q 720 320 820 360 Q 920 400 960 440"
        class="curve7"/>
  
  <!-- 曲线8 - 灰色 -->
  <path d="M 240 460 Q 340 360 440 310 Q 540 260 640 280 Q 740 300 840 340 Q 940 380 980 420"
        class="curve8"/>
  
  <!-- 曲线9 - 黄绿色 -->
  <path d="M 260 440 Q 360 340 460 290 Q 560 240 660 260 Q 760 280 860 320 Q 960 360 1000 400"
        class="curve9"/>
  
  <!-- 曲线10 - 青色 -->
  <path d="M 280 420 Q 380 320 480 270 Q 580 220 680 240 Q 780 260 880 300 Q 980 340 1020 380"
        class="curve10"/>
  
  <!-- 曲线11 - 黑色 (最突出的一条) -->
  <path d="M 300 400 Q 400 300 500 250 Q 600 200 700 220 Q 800 240 900 280 Q 1000 320 1050 360"
        class="curve11"/>
  
  <!-- 添加一些数据点标记 -->
  <g fill="#333" opacity="0.7">
    <circle cx="150" cy="580" r="2"/>
    <circle cx="200" cy="520" r="2"/>
    <circle cx="300" cy="450" r="2"/>
    <circle cx="400" cy="400" r="2"/>
    <circle cx="500" cy="380" r="2"/>
    <circle cx="600" cy="400" r="2"/>
    <circle cx="700" cy="440" r="2"/>
    <circle cx="800" cy="500" r="2"/>
  </g>
  
</svg>
