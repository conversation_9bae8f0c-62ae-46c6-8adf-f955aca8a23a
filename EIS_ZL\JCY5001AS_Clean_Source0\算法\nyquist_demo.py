#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
奈奎斯特图演示脚本
展示如何使用增强版EIS分析工具绘制和分析奈奎斯特图

作者：Jack
日期：2025-01-27
"""

import numpy as np
import matplotlib.pyplot as plt
from enhanced_eis_analyzer import EnhancedEISAnalyzer, CircuitModel
import pandas as pd

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False


def create_sample_data():
    """创建示例EIS数据"""
    print("正在生成示例EIS数据...")
    
    # 频率范围：0.01 Hz 到 1 MHz
    freq = np.logspace(-2, 6, 60)
    omega = 2 * np.pi * freq
    
    # 电池参数（类似您图片中的数据）
    Rs = 5.0      # 溶液电阻 (mΩ)
    Rct = 15.0    # 电荷转移电阻 (mΩ)
    CPE_T = 2e-3  # CPE参数
    CPE_n = 0.82  # CPE指数
    
    # 计算Randles电路阻抗
    Z_CPE = 1 / (CPE_T * (1j * omega)**CPE_n)
    Z_parallel = (Rct * Z_CPE) / (Rct + Z_CPE)
    Z_total = Rs + Z_parallel
    
    # 添加真实的测量噪声
    noise_level = 0.5  # mΩ
    noise_real = np.random.normal(0, noise_level, len(freq))
    noise_imag = np.random.normal(0, noise_level, len(freq))
    
    Z_total += noise_real + 1j * noise_imag
    
    # 转换为mΩ单位（与您的图片一致）
    Z_total *= 1000  # 转换为mΩ
    
    return {
        'freq': freq,
        'z_real': Z_total.real,
        'z_imag': Z_total.imag
    }


def demo_basic_nyquist():
    """演示基本奈奎斯特图绘制"""
    print("\n=== 基本奈奎斯特图演示 ===")
    
    # 创建分析器
    analyzer = EnhancedEISAnalyzer()
    
    # 加载示例数据
    data = create_sample_data()
    analyzer.load_data(data)
    
    # 绘制基本奈奎斯特图
    fig = analyzer.plot_nyquist(show_fit=False)
    plt.title('基本奈奎斯特图 - 类似您提供的图片', fontsize=16, fontweight='bold')
    plt.show()
    
    return analyzer


def demo_circuit_fitting(analyzer):
    """演示等效电路拟合"""
    print("\n=== 等效电路拟合演示 ===")
    
    # 拟合Randles电路
    print("正在拟合Randles电路...")
    randles_result = analyzer.fit_circuit_model(CircuitModel.RANDLES)
    
    if randles_result.success:
        print("✓ Randles电路拟合成功！")
        params = randles_result.parameters
        print(f"  Rs = {params['Rs']:.1f} mΩ")
        print(f"  Rct = {params['Rct']:.1f} mΩ")
        print(f"  CPE_T = {params['CPE_T']:.2e}")
        print(f"  CPE_n = {params['CPE_n']:.3f}")
        print(f"  拟合质量 R² = {randles_result.r_squared:.4f}")
    
    # 拟合双RC电路
    print("\n正在拟合双RC电路...")
    double_rc_result = analyzer.fit_circuit_model(CircuitModel.DOUBLE_RC)
    
    if double_rc_result.success:
        print("✓ 双RC电路拟合成功！")
        print(f"  拟合质量 R² = {double_rc_result.r_squared:.4f}")
    
    # 绘制带拟合曲线的奈奎斯特图
    fig = analyzer.plot_nyquist(show_fit=True)
    plt.title('奈奎斯特图 - 含等效电路拟合', fontsize=16, fontweight='bold')
    plt.show()


def demo_interactive_nyquist(analyzer):
    """演示交互式奈奎斯特图"""
    print("\n=== 交互式奈奎斯特图演示 ===")
    print("提示：点击图上的数据点查看详细信息")
    
    # 高亮特定频率点
    highlight_freqs = [0.1, 1.0, 10.0, 100.0, 1000.0]  # Hz
    
    fig = analyzer.plot_nyquist(
        show_fit=True, 
        interactive=True,
        highlight_frequencies=highlight_freqs
    )
    plt.title('交互式奈奎斯特图 - 点击数据点查看信息', fontsize=16, fontweight='bold')
    plt.show()


def demo_bode_plots(analyzer):
    """演示Bode图"""
    print("\n=== Bode图演示 ===")
    
    fig = analyzer.plot_bode(show_fit=True)
    plt.suptitle('Bode图 - 阻抗幅值和相位', fontsize=16, fontweight='bold')
    plt.show()


def demo_residual_analysis(analyzer):
    """演示残差分析"""
    print("\n=== 残差分析演示 ===")
    
    if CircuitModel.RANDLES in analyzer.fit_results:
        fig = analyzer.plot_residuals(CircuitModel.RANDLES)
        plt.show()
    else:
        print("请先进行Randles电路拟合")


def demo_comprehensive_analysis(analyzer):
    """演示综合分析"""
    print("\n=== 综合分析报告 ===")
    
    # 生成分析报告
    report = analyzer.generate_analysis_report()
    
    print(f"数据信息：")
    print(f"  数据点数: {report['data_info']['data_points']}")
    print(f"  频率范围: {report['data_info']['frequency_range']['min']:.2e} - {report['data_info']['frequency_range']['max']:.2e} Hz")
    print(f"  阻抗范围: {report['data_info']['impedance_range']['real_min']:.1f} - {report['data_info']['impedance_range']['real_max']:.1f} mΩ")
    
    # 模型比较
    comparison = analyzer.compare_models()
    if not comparison.empty:
        print(f"\n模型比较：")
        print(comparison.to_string(index=False))
    
    # 建议
    if report['recommendations']:
        print(f"\n分析建议：")
        for i, rec in enumerate(report['recommendations'], 1):
            print(f"  {i}. {rec}")


def demo_data_from_file():
    """演示从文件加载数据"""
    print("\n=== 从文件加载数据演示 ===")
    
    # 创建示例CSV文件
    data = create_sample_data()
    df = pd.DataFrame(data)
    
    # 保存为CSV文件
    csv_file = "sample_eis_data.csv"
    df.to_csv(csv_file, index=False)
    print(f"已创建示例数据文件: {csv_file}")
    
    # 从文件加载
    analyzer = EnhancedEISAnalyzer()
    if analyzer.load_data(csv_file):
        print("✓ 从CSV文件加载数据成功")
        
        # 快速分析
        analyzer.fit_circuit_model(CircuitModel.RANDLES)
        fig = analyzer.plot_nyquist(show_fit=True)
        plt.title('从CSV文件加载的EIS数据', fontsize=16, fontweight='bold')
        plt.show()
    
    return analyzer


def main():
    """主演示函数"""
    print("奈奎斯特图分析演示程序")
    print("=" * 50)
    
    # 基本演示
    analyzer = demo_basic_nyquist()
    
    # 等效电路拟合
    demo_circuit_fitting(analyzer)
    
    # 交互式图形
    demo_interactive_nyquist(analyzer)
    
    # Bode图
    demo_bode_plots(analyzer)
    
    # 残差分析
    demo_residual_analysis(analyzer)
    
    # 综合分析
    demo_comprehensive_analysis(analyzer)
    
    # 文件加载演示
    demo_data_from_file()
    
    print("\n演示完成！")
    print("\n使用提示：")
    print("1. 可以将您的EIS数据保存为CSV格式，包含freq, z_real, z_imag列")
    print("2. 使用analyzer.load_data('your_file.csv')加载数据")
    print("3. 使用analyzer.plot_nyquist(interactive=True)创建交互式图形")
    print("4. 使用analyzer.fit_circuit_model()进行等效电路拟合")


if __name__ == "__main__":
    main()
